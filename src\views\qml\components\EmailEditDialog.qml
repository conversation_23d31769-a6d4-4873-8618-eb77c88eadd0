/*
 * 邮箱编辑对话框组件
 * 用于编辑邮箱的备注信息和标签
 */

import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls.Material 2.15

Dialog {
    id: root
    title: "编辑邮箱信息"
    modal: true
    width: 500
    height: 650
    anchors.centerIn: parent

    // 对外属性
    property int emailId: 0
    property string emailAddress: ""
    property string originalNotes: ""
    property var originalTags: []
    property var availableTags: []

    // 内部状态
    property var selectedTags: []
    property var filteredTags: []
    property bool isLoading: false

    // 信号
    signal editCompleted(int emailId, string notes, var tagIds)
    signal tagRefreshRequested()
    signal tagCreationRequested(var tagData)

    function openDialog(emailId, emailAddress, notes, tags, allTags) {
        root.emailId = emailId
        root.emailAddress = emailAddress
        root.originalNotes = notes || ""
        root.originalTags = tags || []
        root.availableTags = allTags || []
        
        // 初始化UI
        notesField.text = root.originalNotes
        selectedTags = root.originalTags.slice() // 复制数组
        
        // 过滤可用标签（排除已选择的）
        updateFilteredTags()
        
        open()
    }

    function updateFilteredTags() {
        var filtered = []
        var searchText = tagSearchField.text.toLowerCase()
        
        for (var i = 0; i < availableTags.length; i++) {
            var tag = availableTags[i]
            // 检查是否已选择
            var isSelected = false
            for (var j = 0; j < selectedTags.length; j++) {
                if (selectedTags[j].id === tag.id) {
                    isSelected = true
                    break
                }
            }
            
            // 搜索过滤
            var matchesSearch = !searchText || 
                               (tag.name && tag.name.toLowerCase().includes(searchText))
            
            if (!isSelected && matchesSearch) {
                filtered.push(tag)
            }
        }
        
        filteredTags = filtered
    }

    function addTag(tag) {
        var newSelected = selectedTags.slice()
        newSelected.push(tag)
        selectedTags = newSelected
        updateFilteredTags()
    }

    function removeTag(tagToRemove) {
        var newSelected = []
        for (var i = 0; i < selectedTags.length; i++) {
            if (selectedTags[i].id !== tagToRemove.id) {
                newSelected.push(selectedTags[i])
            }
        }
        selectedTags = newSelected
        updateFilteredTags()
    }

    function getSelectedTagIds() {
        var ids = []
        for (var i = 0; i < selectedTags.length; i++) {
            ids.push(selectedTags[i].id)
        }
        return ids
    }

    background: Rectangle {
        color: "white"
        radius: 12
        border.color: "#e0e0e0"
        border.width: 1

        // 添加阴影效果
        Rectangle {
            anchors.fill: parent
            anchors.margins: -4
            color: "#10000000"
            radius: parent.radius + 4
            z: -1
        }
    }

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 24
        spacing: 20

        // 邮箱信息显示
        Rectangle {
            Layout.fillWidth: true
            height: 60
            color: "#f8f9fa"
            radius: 8
            border.color: "#e9ecef"

            RowLayout {
                anchors.fill: parent
                anchors.margins: 16
                spacing: 12

                Text {
                    text: "📧"
                    font.pixelSize: 20
                    color: "#495057"
                }

                ColumnLayout {
                    Layout.fillWidth: true
                    spacing: 2

                    Text {
                        text: "邮箱地址"
                        font.pixelSize: 11
                        color: "#6c757d"
                        font.weight: Font.Medium
                    }

                    Text {
                        text: root.emailAddress
                        font.pixelSize: 14
                        color: "#212529"
                        font.weight: Font.DemiBold
                        elide: Text.ElideRight
                        Layout.fillWidth: true
                    }
                }
            }
        }

        // 备注编辑区域
        GroupBox {
            Layout.fillWidth: true
            title: "备注信息"
            font.pixelSize: 14
            font.weight: Font.Medium

            background: Rectangle {
                color: "#fafafa"
                radius: 8
                border.color: "#e0e0e0"
            }

            ColumnLayout {
                anchors.fill: parent
                spacing: 12

                // 备注输入框 - 修复文字定位问题
                TextArea {
                    id: notesField
                    Layout.fillWidth: true
                    Layout.preferredHeight: 100

                    // 基本属性设置
                    font.pixelSize: 13
                    color: "#333"
                    selectByMouse: true
                    wrapMode: TextArea.Wrap

                    // 关键修复：设置内边距确保文字显示在输入框内部
                    leftPadding: 12
                    rightPadding: 12
                    topPadding: 12
                    bottomPadding: 12

                    // 自定义背景样式
                    background: Rectangle {
                        color: "white"
                        radius: 6
                        border.color: notesField.activeFocus ? "#2196F3" : "#e0e0e0"
                        border.width: notesField.activeFocus ? 2 : 1
                    }
                }
            }
        }

        // 标签编辑区域
        GroupBox {
            Layout.fillWidth: true
            Layout.fillHeight: true
            title: "标签管理"
            font.pixelSize: 14
            font.weight: Font.Medium

            background: Rectangle {
                color: "#fafafa"
                radius: 8
                border.color: "#e0e0e0"
            }

            ColumnLayout {
                anchors.fill: parent
                spacing: 12

                Label {
                    text: "选择或创建标签来分类管理邮箱"
                    font.pixelSize: 12
                    color: "#666"
                    wrapMode: Text.WordWrap
                    Layout.fillWidth: true
                }

                // 已选择的标签
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 80
                    color: "white"
                    radius: 6
                    border.color: "#e0e0e0"
                    visible: selectedTags.length > 0

                    ColumnLayout {
                        anchors.fill: parent
                        anchors.margins: 12
                        spacing: 8

                        Label {
                            text: "已选择的标签 (" + selectedTags.length + ")"
                            font.pixelSize: 12
                            font.weight: Font.Medium
                            color: "#495057"
                        }

                        Flow {
                            Layout.fillWidth: true
                            spacing: 6

                            Repeater {
                                model: selectedTags
                                
                                Rectangle {
                                    width: tagContent.width + 16
                                    height: 28
                                    color: modelData.color || "#2196F3"
                                    radius: 14

                                    RowLayout {
                                        id: tagContent
                                        anchors.centerIn: parent
                                        spacing: 4

                                        Text {
                                            text: modelData.icon || "🏷️"
                                            font.pixelSize: 12
                                        }

                                        Text {
                                            text: modelData.name || ""
                                            font.pixelSize: 11
                                            color: "white"
                                            font.weight: Font.Medium
                                        }

                                        Button {
                                            text: "✕"
                                            implicitWidth: 16
                                            implicitHeight: 16
                                            font.pixelSize: 10
                                            flat: true
                                            
                                            background: Rectangle {
                                                color: parent.hovered ? "#ffffff40" : "transparent"
                                                radius: 8
                                            }
                                            
                                            contentItem: Text {
                                                text: parent.text
                                                color: "white"
                                                font: parent.font
                                                horizontalAlignment: Text.AlignHCenter
                                                verticalAlignment: Text.AlignVCenter
                                            }
                                            
                                            onClicked: removeTag(modelData)
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // 标签搜索 - 浮动标签效果
                Item {
                    Layout.fillWidth: true
                    height: 58  // 增加高度以容纳浮动标签

                    Rectangle {
                        id: searchContainer
                        anchors.fill: parent
                        anchors.topMargin: 8  // 为浮动标签留出空间
                        color: "white"
                        radius: 6
                        border.color: tagSearchField.activeFocus ? "#2196F3" : "#e0e0e0"
                        border.width: tagSearchField.activeFocus ? 2 : 1

                        RowLayout {
                            anchors.fill: parent
                            anchors.margins: 12
                            spacing: 8

                            Text {
                                text: "🔍"
                                font.pixelSize: 14
                                color: "#666"
                            }

                            TextField {
                                id: tagSearchField
                                Layout.fillWidth: true
                                font.pixelSize: 13
                                color: "#333"
                                selectByMouse: true

                                // 移除placeholder，使用浮动标签代替
                                background: Rectangle {
                                    color: "transparent"
                                }

                                onTextChanged: updateFilteredTags()
                            }
                        }
                    }

                    // 浮动标签
                    Rectangle {
                        id: floatingLabel
                        x: 48  // 右移以避免覆盖搜索图标
                        y: tagSearchField.activeFocus || tagSearchField.text.length > 0 ? 0 : 22
                        width: floatingLabelText.implicitWidth + 8
                        height: 16
                        color: "white"
                        visible: true

                        Text {
                            id: floatingLabelText
                            anchors.centerIn: parent
                            text: "搜索或创建标签"
                            font.pixelSize: tagSearchField.activeFocus || tagSearchField.text.length > 0 ? 11 : 14
                            color: tagSearchField.activeFocus ? "#2196F3" : "#666"
                        }

                        Behavior on y { PropertyAnimation { duration: 200; easing.type: Easing.OutCubic } }
                        Behavior on color { PropertyAnimation { duration: 200 } }
                    }

                        Button {
                            text: "🏷️ 新建"
                            font.pixelSize: 11
                            implicitHeight: 24
                            flat: true
                            visible: tagSearchField.text.length > 0
                            
                            background: Rectangle {
                                color: parent.hovered ? "#2196F3" : "#e3f2fd"
                                radius: 4
                            }
                            
                            contentItem: Text {
                                text: parent.text
                                font: parent.font
                                color: parent.hovered ? "white" : "#2196F3"
                                horizontalAlignment: Text.AlignHCenter
                                verticalAlignment: Text.AlignVCenter
                            }
                            
                            onClicked: {
                                var tagData = {
                                    name: tagSearchField.text.trim(),
                                    description: "",
                                    icon: "🏷️",
                                    color: "#2196F3"
                                }
                                root.tagCreationRequested(tagData)
                            }
                        }
                    }
                }

                // 可选标签列表 - 简化实现确保正常显示
                ListView {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    visible: filteredTags.length > 0

                    model: filteredTags
                    spacing: 4
                    clip: true  // 启用剪裁

                        delegate: Rectangle {
                            width: ListView.view.width
                            height: 36
                            color: mouseArea.containsMouse ? "#f8f9fa" : "white"
                            radius: 6
                            border.color: "#e9ecef"

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 8
                                spacing: 8

                                Rectangle {
                                    width: 20
                                    height: 20
                                    color: modelData.color || "#2196F3"
                                    radius: 10

                                    Text {
                                        anchors.centerIn: parent
                                        text: modelData.icon || "🏷️"
                                        font.pixelSize: 10
                                    }
                                }

                                Text {
                                    Layout.fillWidth: true
                                    text: modelData.name || ""
                                    font.pixelSize: 12
                                    color: "#495057"
                                    elide: Text.ElideRight
                                }

                                Text {
                                    text: (modelData.usage_count || 0) + " 次使用"
                                    font.pixelSize: 10
                                    color: "#6c757d"
                                }
                            }

                            MouseArea {
                                id: mouseArea
                                anchors.fill: parent
                                hoverEnabled: true
                                onClicked: addTag(modelData)
                            }
                        }
                    }

                // 无标签提示
                Rectangle {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: "transparent"
                    visible: filteredTags.length === 0 && tagSearchField.text.length === 0

                    ColumnLayout {
                        anchors.centerIn: parent
                        spacing: 12

                        Text {
                            text: "🏷️"
                            font.pixelSize: 24
                            color: "#adb5bd"
                            Layout.alignment: Qt.AlignHCenter
                        }

                        Text {
                            text: "没有可用的标签"
                            font.pixelSize: 12
                            color: "#6c757d"
                            Layout.alignment: Qt.AlignHCenter
                        }

                        Button {
                            text: "刷新标签列表"
                            font.pixelSize: 11
                            Layout.alignment: Qt.AlignHCenter
                            onClicked: root.tagRefreshRequested()
                        }
                    }
                }
            }
        }

        // 操作按钮
        RowLayout {
            Layout.fillWidth: true
            spacing: 12

            Button {
                text: "取消"
                Layout.preferredWidth: 100
                onClicked: root.close()
            }

            Item { Layout.fillWidth: true }

            Button {
                text: isLoading ? "保存中..." : "保存"
                Layout.preferredWidth: 100
                Material.background: Material.Blue
                enabled: !isLoading
                onClicked: {
                    isLoading = true
                    var tagIds = getSelectedTagIds()
                    root.editCompleted(root.emailId, notesField.text, tagIds)
                }
            }
        }
    }

    onClosed: {
        // 重置状态
        isLoading = false
        tagSearchField.text = ""
        selectedTags = []
        filteredTags = []
    }
}